# Tools & Frameworks

Tools and frameworks that should be considered for building LLM based POCs and demo solutions.

## Inference providers

Hosted LLMs that provide free quota can be used for most POCs and demo solutions.

- **Gemini**: Generous free limits. API key from [Google AI Studio](https://ai.dev). Flash models recommended.
- **Groq**: Free tier available. API key from [Groq Console](https://console.groq.com/keys).
- **Github Models**: Free quota for prototyping via [Copilot Free tier](https://docs.github.com/en/github-models/use-github-models/prototyping-with-ai-models).
- **Cohere**: Free limits with a [trial key](https://docs.cohere.com/v2/docs/rate-limits) (Cohere models only).

## Prompt Engineering

- [DSPy](https://www.youtube.com/watch?v=I9ZtkgYZnOw)

## Embedding

Cohere [embedding models](https://cohere.com/blog/embed-4) can be used here. Their [reranking model](https://docs.cohere.com/v2/reference/rerank) is also pretty good.

Good models to produce embeddings & perform reranking locally

- [Qwen3-Embeddings](https://huggingface.co/collections/Qwen/qwen3-embedding-6841b2055b99c44d9a4c371f)
- [Qwen3-Reranker](https://huggingface.co/collections/Qwen/qwen3-reranker-6841b22d0192d7ade9cdefea)

[Fastembed](https://github.com/qdrant/fastembed) is a good library to generate embeddings. 

## Chunking

Using the right chunking strategy is important for getting good results. Consider below libraries

[Chonkie](https://www.chonkie.ai) library can be considered for chunking.

## Web Search & Crawl

Tools that can be used with Agents. Each has free quota.

- **Web Search**
  - [Linkup](https://www.linkup.so/)
  - [Serper](https://serper.dev/)
  - [Tavily](https://www.tavily.com)

- **Web Crawlers**
  - [Crawlee](https://crawlee.dev/)

## Observability

Observability tools that can be used for tracing LLM calls.

- [Opik](https://www.comet.com/site/products/opik/)
- [Langfuse](https://langfuse.com/)
- [Logfire](https://pydantic.dev/logfire)
