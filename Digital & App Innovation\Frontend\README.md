# Foundation Training Plan - Frontend Web Developer

## Table of Contents

[Introduction](#introduction)

[Prerequisites](#prerequisites)

[Training Plan](#training-plan)

[Capstone Projects](#capstone-projects)

#

<a name="introduction"></a>
## Introduction

This is a foundation training plan for **frontend web developer**. The role involves building websites. A _typical_ career progression for a frontend engineer would look like below

![](./img/career-path.png)

Below is a synopsis of each of the above roles for a general understanding of what is involved in frontend engineering

- **Junior Front-End Developer:** The role starts as a junior front-end developer, where foundational skills such as HTML, CSS, JavaScript, and basic web development concepts are learned. Practical experience is gained by working on small projects and collaborating with senior developers.
- **Mid-Level Front-End Developer:** Proficiency in front-end frameworks and libraries like React, Angular, or Vue.js is achieved. A deeper understanding of JavaScript and exploration of more complex web development concepts such as responsive design, performance optimization, and accessibility are undertaken. Ownership of larger projects is gradually assumed, and contributions to architectural decisions are made.
- **Senior Front-End Developer:** Expertise is developed in advanced front-end technologies, best practices, and design patterns. Strong problem-solving abilities and leadership in front-end development efforts are demonstrated. Mentorship of junior developers, contribution to code reviews, and provision of technical guidance to the team occur. More responsibility for project planning, architecture, and technical decision-making is taken on.
- **Front-End Lead** : A transition is made into a leadership role overseeing front-end development teams, coordinating projects, and guiding the technical direction of front-end initiatives. Management of resources, mentoring and support of team members, and close collaboration with stakeholders to deliver high-quality web experiences are involved. This role combines technical and managerial skills.
- **Front-End Architect:** Expertise is gained as a subject matter expert in front-end architecture, design patterns, and emerging technologies. Strategic guidance is provided for large-scale front-end projects, collaboration with cross-functional teams occurs, and scalability, performance, and maintainability of web applications are ensured. Staying up-to-date with industry trends and contributing to the broader front-end development community through conferences, articles, or open-source contributions are important aspects of this role.

#

<a name="prerequisites"></a>
## Prerequisites

Complete the instructions [here](../../README.md).

<a name="training-plan"></a>
## Training Plan

**Duration: 2 Weeks**

**Option 1**

[FCC Responsive Web Design Course](https://www.freecodecamp.org/learn/2022/responsive-web-design) is a free course on Responsive Web Design. It is a great way to get started with web development.

**Option 2**

[MDN](https://developer.mozilla.org/en-US/) has the best resources for foundational web development training. This plan will simply follow the [MDN frontend learning pathway](https://developer.mozilla.org/en-US/docs/Learn/Front-end_web_developer).


**Week 1:** 
- [**HTML – Structuring the web**](https://developer.mozilla.org/en-US/docs/Learn/HTML)

- [**CSS – Styling the web**](https://developer.mozilla.org/en-US/docs/Learn/CSS)

**Week 2: Scripting & User data**

- [JavaScript](https://developer.mozilla.org/en-US/docs/Learn/JavaScript)
  - [JavsScript Best Practices](https://blog.jetbrains.com/webstorm/2024/10/javascript-best-practices-2024/)
- [Web forms](https://developer.mozilla.org/en-US/docs/Learn/Forms)

Note: use [jsonplaceholder](https://jsonplaceholder.typicode.com/) for hosted fake api's.

**Option 3**: 

The [foundations course](https://www.theodinproject.com/paths/foundations/courses/foundations) by _the odin project_ is a good alternative to MDN course above. Prerequisites would be good to cover from [here](https://www.theodinproject.com/paths/foundations/courses/foundations#prerequisites). Advanced candidates may finish the [full stack JavaScript path](https://www.theodinproject.com/paths/full-stack-javascript) on their own.

**Online courses** : one can also complete below online courses

- [https://www.coursera.org/learn/introduction-to-front-end-development](https://www.coursera.org/learn/introduction-to-front-end-development)
- [https://www.coursera.org/learn/html-and-css-in-depth](https://www.coursera.org/learn/html-and-css-in-depth)
- [https://www.coursera.org/learn/programming-with-javascript](https://www.coursera.org/learn/programming-with-javascript)

#

<a name="capstone-projects"></a>
## Capstone Projects

Once fundamentals are covered and hands-on proficiency is achieved w.r.t. various web development concepts, one can work on below projects and produce demonstratable output

**Note** : There will be no backend yet for the applications, all data would be stored in [client side storage](https://developer.mozilla.org/en-US/docs/Learn/JavaScript/Client-side_web_APIs/Client-side_storage#new_school_web_storage_and_indexeddb).

**Project 1: Library Management Application**

This capstone project involves creating a web-based Library Management Application that aims to streamline and automate the management tasks associated with running a library. The application will serve as a digital platform, providing librarians, administrators, and library users with the means to easily interact with the library's collection of books and resources.

1. User Registration and Authentication:
  - Allow users to create accounts and log in securely.
  - Implement authentication mechanisms to ensure secure access to user-specific features.
  - Provide options for password reset and account management.
2. Book Management:
  - Enable librarians/administrators to add, update, and delete books in the library.
  - Include fields for book title, author, genre, ISBN, publication date, and availability status.
  - Implement search and filtering options to allow users to find books based on various criteria.
3. User Borrowing and Return:
  - Allow registered users to borrow books from the library.
  - Implement a checkout system to track borrowed books and due dates.
  - Send notifications or reminders for overdue books and manage the return process.
4. Book Reservations:
  - Enable users to reserve books that are currently unavailable.
  - Implement a reservation queue system to handle multiple requests for the same book.
  - Notify users when reserved books become available for borrowing.
5. User Management:
  - Provide administrative features to manage user accounts, including adding, updating, and deleting user profiles.
  - Implement role-based access control to differentiate between librarians/administrators and regular users.
6. Book Reviews and Ratings:
  - Allow users to rate and review books they have read.
  - Display average ratings and user reviews to help other users make informed decisions.

**Project 2: WideWorldImporters Web Application**

This project will be based on the [Wide World Importers use case](https://learn.microsoft.com/en-us/sql/samples/wide-world-importers-what-is?view=sql-server-ver16) created for MS SQL Server sample database. The requirement is to create a frontend web app with below features

1. Product Catalog:
  - Create a web application to showcase the Wide World Importers product catalog.
  - Display product details, including descriptions, prices, and availability.
  - Implement search and filtering functionality to help users find desired products.
2. Customer Order Management:
  - Enable customers to add products to a shopping cart and place orders.
  - Develop a checkout process to collect shipping and payment information.
  - Provide order confirmation and tracking details to customers.
3. Inventory Tracking:
  - Implement inventory management to track available stock for each product.
  - Display real-time stock status on the product pages.
  - Notify customers when a product is out of stock or low in quantity.
4. User Registration and Authentication:
  - Create user accounts for customers to store their order history and preferences.
  - Implement user registration and login functionality.
  - Secure customer information and ensure data privacy.
5. Administration Dashboard:
  - Develop an administration dashboard for Wide World Importers staff.
  - Enable staff members to manage product inventory, update prices, and add new products.
  - Provide order management features, including order processing and shipment tracking.
6. Responsive Design:
  - Ensure the web application is mobile-friendly and responsive across devices.
  - Optimize the user interface for easy navigation and seamless user experience.

  
