# Backend Development Training

This section provides comprehensive training plans for building robust and scalable backend applications. The plans are categorized by framework to help you specialize in your area of interest.

## Available Training Plans

- **[.NET](./DOTNET/README.md)**: Learn to build powerful backend services with C# and the .NET ecosystem.
- **[Node.js](./NodeJS/README.md)**: Master backend development using JavaScript and Node.js for fast, event-driven applications.
