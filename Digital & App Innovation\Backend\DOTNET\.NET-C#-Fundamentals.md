# .NET & C# Fundamentals

.NET is an open-source, general purpose development platform maintained by Microsoft and the .NET community. It is a modular runtime and library implementation that supports different architectures and platforms, including Windows, Linux and macOS. 

C# is an object-oriented, component-oriented programming language that can be used to build applications and services on .NET Core. The C# code is compiled into intermediate language (IL) that is executed by the .NET Core Common Language Runtime (CLR).

## Training Plan

### Duration: 3 Weeks

### Week 1: C# and .NET Fundamentals

- Introduction to .NET Framework and CLR
- Managed vs unmanaged code
- Common Language Runtime overview
- Assemblies, MSIL, JIT compilation
- NuGet and package management
- Introduction to C#
- Data types, variables, operators
- Control flows - if/else, loops, switch  
- Arrays, lists, dictionaries
- Methods, parameters, return values
- Classes vs objects, encapsulation
- Constructors, properties, fields
- Nullable, nameof, discard, etc.

### Week 2: OOP, Advanced C# Concepts and Version Features

- Object oriented principles
- Inheritance, polymorphism
- Structs vs classes
- Interfaces and abstract classes
- Generics
- Delegates and events
- Exception handling
- Working with files/streams
- LINQ and lambda expressions
- Async/await, multithreading
- Regular expressions
- Expression bodies, auto property initializers
- Tuples, pattern matching
- Nullable reference types
- Record types, top level programs
- Global usings, file-scoped namespaces

### Week 3: Databases with EF Core and More Features

- Introduction to Entity Framework Core
- DbContext, database connections
- Code First modeling - entities, relationships
- Migrations, querying database
- CRUD operations with EF Core
- Dynamic, expando objects
- Reflection and attributes
- Unsafe code, pointers
- Memory management
- Task parallel library

Throughout the course, build console apps to demonstrate concepts learned. Include coding exercises, review questions and real world examples.

Finish the free course and certification here: https://www.freecodecamp.org/learn/foundational-c-sharp-with-microsoft/

Once above is done, finish this [design patterns course](https://www.youtube.com/watch?v=rylaiB2uH2A). This can be done on the side while working on projects.

## Resources

- [.NET Videos](https://dotnet.microsoft.com/en-us/learn/videos)
- [Youtube Videos](https://www.youtube.com/playlist?list=PLdo4fOcmZ0oULFjxrOagaERVAMbmG20Xe)
