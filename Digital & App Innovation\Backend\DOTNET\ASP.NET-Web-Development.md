# Web Development with ASP.NET Core

ASP.NET Core is an open-source and cross-platform framework for building modern cloud-based web applications using .NET. It combines the ASP.NET MVC framework, Razor pages and Entity Framework Core to provide a robust web development platform with minimal overhead. ASP.NET Core applications run on .NET Core which works on Windows, Linux and MacOS allowing developers to build and deploy apps to multiple environments. The framework is fully modular so developers can include only the components their apps require.

## Training Plan

### Duration: 4 weeks

### Week 1: ASP.NET Core Fundamentals

- Introduction to ASP.NET Core
  - Overview of key concepts and advantages
  - Differences between ASP.NET Core and ASP.NET
- Getting started
  - Setting up development environment
  - Creating a basic ASP.NET Core web app
  - Project structure and files
  - Launching the app
- Routing and controllers
  - Configuring routes
  - Adding controllers
  - Return types - views, JSON
  - Route parameters
- Razor syntax and views
  - Razor view engine
  - Layouts, partial views, view components
  - Tag helpers
  - View models
  
### Week 2: ASP.NET Core MVC

- MVC architecture and patterns
- Models
  - Entity Framework Core
  - Data annotations
- Views
  - HTML helpers
  - Forms and validation
- Controllers
  - Action methods
  - Dependency injection
- Authentication and authorization
  - User management
  - Policies and roles
  
### Week 3: API Development

- Building APIs
  - Return JSON data
  - HTTP methods
  - Versioning APIs  
- Swagger documentation
- Entity Framework Core
  - Database context
  - Migrations
  - CRUD operations
- Authentication and authorization
  - JWT tokens
  - Securing APIs
  
### Week 4: Capstone Project

- Planning requirements and scope
- Developing an MVC web application 
  - Front-end with Razor views
  - Back-end with Entity Framework Core
  - Authentication and authorization
- Building a RESTful API
- Adding Swagger documentation
- Deploying app to Azure

## Resouces
-- [.NET Videos] (https://dotnet.microsoft.com/en-us/learn/dotnet/hello-world-tutorial/intro)
