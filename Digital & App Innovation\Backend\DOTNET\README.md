# .NET Backend Development Learning Path

Welcome to the .NET backend development training program! This guide provides a structured path to help you learn the essentials of building robust backend applications with .NET and C#.

The training is divided into key areas, starting with the fundamentals and progressing to more advanced web development topics.

---

## 1. Core Concepts & Fundamentals

Before diving into web development, it's crucial to have a solid understanding of the C# language and the .NET framework.

- **Training Plan**: **[.NET & C# Fundamentals](./.NET-C%23-Fundamentals.md)** (To be completed)
- **Essential Skill**: [Debugging in Visual Studio](https://www.youtube.com/watch?v=Dr4Y58nqxVs) - *Learn how to effectively debug your applications.*

### Recommended Foundational Learning

- **Books**:
  - [C# in Depth by <PERSON>](https://amzn.to/44glvRU) - *A highly-regarded book for a deep understanding of C#.*
  - [C# 11 and .NET 7 by <PERSON>](https://amzn.to/46g9bTl) - *A comprehensive guide to the latest C# and .NET versions.*
- **Video Tutorials**:
  - [I am <PERSON>](https://bit.ly/3UD0zAI) - *Excellent for practical, real-world C# and .NET tutorials.*
  - [Programming with Mosh](https://bit.ly/3KEuLqL) - *High-quality, well-explained tutorials for beginners.*
  - [Kudvenkat](https://bit.ly/3MGsSfK) - *In-depth playlists covering C# and .NET from the ground up.*

---

## 2. Web Development with ASP.NET Core

Once you have a grasp of the fundamentals, you can move on to building web applications and APIs with ASP.NET Core.

- **Training Plan**: **[Web Development with ASP.NET Core](./ASP.NET-Web-Development.md)** (To be completed)

### Recommended ASP.NET Core Learning Resources

- **Book**: [ASP.NET Core in Action by Andrew Lock](https://amzn.to/3qXY1T0) - *A practical, project-based guide to learning ASP.NET Core.*
- **Blog**: [Andrew Lock's Blog](https://andrewlock.net/) - *A fantastic resource for detailed articles on ASP.NET Core.*
- **YouTube Channels**:
  - [Nick Chapsas](https://bit.ly/3MKFhiz) - *Focuses on modern .NET development practices and performance.*
  - [Milan Jovanović](https://bit.ly/3zXoPEi) - *Covers clean architecture and best practices in .NET.*

---

## 3. Frontend Web Development with Blazor

For those interested in building interactive web UIs using C#, Blazor is the go-to framework.

- **Training Plan**: **[Web Development with Blazor](./Blazor-Web-Development.md)** (To be completed)

### Recommended Blazor Learning Resources

- **Book**: [Web Development in Blazor](https://amzn.to/3qZfH0E)
- **YouTube Channel**: [Gui Ferreira](https://lnkd.in/dwQmNskm)

---

## Additional Resources & References

This section contains a broader list of valuable resources to supplement your learning and stay up-to-date with the .NET ecosystem.

### Newsletters

- [C# Digest](https://csharpdigest.net/)
- [Milan Jovanovic](https://www.milanjovanovic.tech/newsletter)
- [Stefan Dokic](https://stefandjokic.tech/)

### More YouTube Channels

- [Official .NET Channel](https://bit.ly/3L2CUqn)
- [Code Opinion](https://lnkd.in/dck-7-P2)
- [freeCodeCamp](https://bit.ly/3GMDVQC)
- [Shawn Wildermuth](https://lnkd.in/dUhDxmth)
- [Les Jackson](https://lnkd.in/dSv2bvdn)

### Blogs & Websites

- [Code Maze](https://code-maze.com/)
- [Code with Mukesh](https://codewithmukesh.com)
- [Steve Smith (Ardalis)](https://ardalis.com/blog/)
- [I Love Dotnet](https://ilovedotnet.org/)
- [JetBrains .NET Blog](https://blog.jetbrains.com/dotnet/)

### E-Books (Free)

- [DevOps for .NET](https://bit.ly/3KGcBoC)
- [.NET Core](https://bit.ly/43rc2rc)
- [Microservices with .NET](https://bit.ly/3GC5yfe)
- [Azure e-books](https://bit.ly/3ocaTn2)
- [MAUI e-books](https://bit.ly/3ob239n)

### Essential Software Development Books

- [Clean Code by Robert C. Martin](https://amzn.to/3r486xF) - *A must-read for any professional developer.*
