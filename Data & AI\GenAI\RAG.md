# Retrieval-Augmented Generation

**R**etrieval-**A**ugmented **G**eneration (RAG), which is an approach to enhancing language models with external knowledge retrieval capabilities. In the context of large language models (LLMs), RAG aims to address the limitations of these models in accessing and utilizing external knowledge beyond what is contained in their training data. While LLMs can generate coherent and fluent text based on the patterns they have learned from their training data, they may struggle with tasks that require accessing and reasoning over factual knowledge that is not present in their training data. Cover below course on vector databases in addition to courses on LLM's in Gen AI readme.

- [Building apps with vector dbs](https://www.deeplearning.ai/short-courses/building-applications-vector-databases/)

## Building RAG Applications

RAG applications are the most popular type of applications in the LLM space. LLMs are typically trained on vast amounts of general information and don’t have domain/task specific information. RAG applications augment LLM capabilities with domain specific data.

### Chunking strategies

When you have domain-specific data, you need to break it down into smaller pieces to fit the token limit of embedding models. This process is called chunking. The way you chunk your data can have a significant impact on the performance of your RAG application. Review below resources for chunking strategies.

- [Databricks Guide](https://community.databricks.com/t5/technical-blog/the-ultimate-guide-to-chunking-strategies-for-rag-applications/ba-p/113089)
- [Pinecone Guide](https://www.pinecone.io/learn/chunking-strategies/)
- [Cohere Cookbook](https://docs.cohere.com/v2/page/chunking-strategies)

### Vector DBs 🔍

- [pgai](https://github.com/timescale/pgai)
  - Preferred since this can be used with postgres database
- Qdrant
- LanceDB
- ChromaDB
- Pinecone
- Weaviate

> **[Relevant Course](https://www.deeplearning.ai/short-courses/retrieval-optimization-from-tokenization-to-vector-quantization/)** ⭐

### Chatbot

There are multiple choices for building chatbot interface. Choose one of below

[Gradio](https://www.gradio.app/)

[Streamlit](https://streamlit.io/)

[CopilotKit](https://github.com/CopilotKit/CopilotKit)

## Advanced RAG

The final result in a RAG system is only as good as the documents retrieved corresponding to the query. New techniques emerge continuously to improve the accuracy of RAG systems. Enhance the Naïve RAG implemented above with below. Make sure to use local LLM

1. Implement various techniques outlined in [this series](https://www.youtube.com/playlist?list=PLfaIDFEXuae2LXbO1_PKyVJiQ23ZztA0x).
    1. Series as a [single tutorial](https://www.youtube.com/watch?v=sVcwVQRHIc8)
2. Couple techniques in [this short course](https://www.deeplearning.ai/short-courses/building-evaluating-advanced-rag/).
3. Implement [RAG Agents](https://www.youtube.com/watch?v=bq1Plo2RhYI). Use free [Gemini LLMs](https://ai.dev) if your machine config doesn't support running local LLMs.
4. Refer extensive collection of [RAG Techniques](https://github.com/NirDiamant/RAG_Techniques).

## MS Courses

- [Rag Time](https://www.youtube.com/playlist?list=PLlrxD0HtieHirdQ1SKCB0SvxiAazYg2VI)

## Current Landscape

- [RAG Architectures](https://www.newsletter.swirlai.com/p/the-evolution-of-modern-rag-architectures)

### UseCase

- Implement [NotebookLM](https://notebooklm.google/) features.
