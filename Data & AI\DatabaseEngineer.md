# Database Programmer, Engineer training plan

## Table of Contents

[Introduction](#introduction)

[Prerequisites](#prerequisites)

[Training Plan](#training-plan)

[Capstone Projects](#capstone-projects)

<a name="introduction"></a>
## Introduction

This is a training plan for **database programmer & engineer**. Data lies at the heart of all solutions that software engineering attempts to provide for real world business problems. Command on database programming & engineering is the steppingstone to being a successful Data Engineer, Data Scientist, or an AI/ML engineer. Starting with a solid foundation in database programming, one can gradually develop their expertise and take on more challenging roles.

A _typical_ career progression in the Data & AI field would look like below

![career path](./img/career-path.png)

**Note**: Data engineering skills greatly help in becoming a good data scientist but are not always essential._

Below is a synopsis of each of the above roles for a general understanding of what is involved in the field of Data & AI

- **Database Programmer** : A Database Programmer is responsible for designing, implementing, and maintaining databases. They have a strong understanding of database concepts, data modeling, and query languages. A database programmer focuses on writing efficient SQL queries and stored procedures to retrieve, update, and manipulate data within a database. They work closely with developers and data analysts to understand business requirements and design database structures that support application functionalities. Database programmers optimize query performance, implement database constraints, and ensure data integrity through proper validation and error handling.
- **Database Engineer** : A database engineer has a broader role, encompassing the design, optimization, and administration of databases within an organization. They handle tasks such as database architecture, performance tuning, capacity planning, and database security implementation. Database engineers work closely with developers, system administrators, and data analysts to ensure efficient and reliable data management. They are responsible for database backups, recovery, and disaster planning to maintain data availability and integrity. Database engineers possess advanced knowledge of database management systems, data modelling, performance optimization, and troubleshooting techniques, allowing them to handle complex database-related challenges.
- **Data Engineer** : As a Data Engineer, professionals take on a broader role in managing data infrastructure and workflows. They are responsible for designing, building, and maintaining scalable data pipelines and ETL (Extract, Transform, Load) processes. Data Engineers work with various data storage technologies, such as relational databases, NoSQL databases, and data warehouses. They also integrate data from different sources, perform data transformations, and ensure data quality and consistency. Their work involves managing big data, implementing data governance, and collaborating with cross-functional teams to support data-driven decision-making.
- **Data Scientist** : Data Scientists focus on analyzing and deriving insights from data to solve complex problems and make data-driven decisions. They utilize statistical analysis, machine learning, and predictive modeling techniques to uncover patterns, trends, and correlations in large datasets. Data Scientists design and implement experiments, build predictive models, and communicate findings to stakeholders. They work closely with Data Engineers and AI/ML Engineers to access and preprocess data, develop models, and deploy them in production environments.
- **AI/ML Engineer** : AI/ML Engineers apply machine learning and artificial intelligence techniques to build intelligent systems and deploy AI models in production. They work with data engineers, data scientists, and software engineers to develop scalable AI/ML solutions. AI/ML Engineers utilize their expertise in data engineering, database management, and model deployment to design efficient data pipelines, implement model training and evaluation workflows, and integrate AI capabilities into applications.

<a name="prerequisites"></a>
## Prerequisites

To start database programming, you will need a database server and a database client installed at the minimum. Databases store all the data on the hard drive, in files. Each has its own format optimized for storage which means it's not plain text or human readable. To enable querying of data, databases implement a service/daemon. Finally, there is a client from which queries could be executed, that is either a console/terminal (the CLI) or a more human friendly interface, the GUI. This training plan focusses on Relational Database Management Systems (RDMS) so we shall install the two most popular RDMS' as of today – Microsoft SQL Server & Postgres. The functionalities in the two are identical for general purposes and you to gain a hands-on experience with both without much extra effort. Below depicts the components involved

![](./img/db-client-server.png)

**Note**: For development purposes you will install both the server & the client on your local machine. In the real-world scenarios only the client will be on your local machine while the server will be on a different machine /VM or cloud. You will always connect your client to the server to be able to perform any database queries._

**MS SQL Server**

Download and install the free SQL Server developer edition from [here](https://www.microsoft.com/en-in/sql-server/sql-server-downloads). Choose the mixed mode for authentication on the database engine configuration dialog and use "Add Current User"

![](./img/sql-server-engine-config.png)

This will setup the server database engine for you. You can also install the [sqlcmd](https://learn.microsoft.com/en-us/sql/tools/sqlcmd/sqlcmd-utility?view=sql-server-ver16) utility to create database, execute queries from the command prompt (CLI) after this step.

While above works for ad hoc queries, it's **not**** productive** for database developers, so we will install the client called SQL Server Management Studio (SSMS) for interacting with the database (engine/server) with ease. Documentation for SSMS along with installation [here](https://learn.microsoft.com/en-us/sql/ssms/download-sql-server-management-studio-ssms?view=sql-server-ver16). Instructions for using SSMS [here](https://learn.microsoft.com/en-us/sql/ssms/quickstarts/ssms-connect-query-sql-server?view=sql-server-ver16).

**Postgres**

Postgres is another popular relational database today. Postgres can be installed from [here](https://www.postgresql.org/download/). This installs the database engine/server. Next, we need to install a postgres client to be able to interact with the server with ease. There are two options

- [pgAdmin](https://www.pgadmin.org/download/)
- [DBeaver](https://dbeaver.io/)

Install any one of the above, although DBeaver is more feature rich as a client. This is same as installing the client for SQL Server, the SSMS.

**Note**: [_Azure Data Studio_](https://learn.microsoft.com/en-us/sql/azure-data-studio/?view=sql-server-ver16) is also a good tool/client of choice once you're familiar with above tools.

<a name="training-plan"></a>
## **Training Plan**

**Duration: 12 Weeks**

**Weeks 1-2: Introduction to Databases and SQL Fundamentals**

**Introduction to Databases**

- What is a database?
- Importance of databases in software development.
- Types of databases (relational, NoSQL, etc.).

**Relational Databases**

- Understanding tables, columns, and rows.
- Primary keys and foreign keys.

**SQL Fundamentals**

- Introduction to SQL and its purpose.
- Writing basic SQL queries (SELECT, INSERT, UPDATE, DELETE).
- Filtering and sorting data.
- Understanding data types and constraints.

**Weeks 3-4: Database Design and Normalization**

**Importance of Good Database Design**

- Understanding the impact of database design on overall system performance and efficiency.
- Avoiding data redundancy and inconsistency through proper design.
- Ensuring data integrity and accuracy.
- Enhancing scalability and flexibility for future system changes.
- Facilitating efficient data retrieval and query optimization.
- Supporting application requirements and business objectives.

**Entity-Relationship Diagrams (ERDs)**

- Creating ERDs to Visualize Database Structure
- Using draw.io to create ERDs.
- Identifying and representing entities, attributes, and relationships visually.
- Understanding the purpose of primary keys and foreign keys in establishing relationships.
- Drawing entity boxes, attribute ovals, and relationship lines in ERDs.
- Identifying Entities, Attributes, and Relationships
- Analyzing the business requirements and identifying entities (e.g., customers, products, orders).
- Defining the attributes for each entity (e.g., customer name, product price, order date).
- Determining the relationships between entities (one-to-one, one-to-many, many-to-many).
- Assigning appropriate cardinality (e.g., 1:1, 1:N, M:N) and participation constraints (mandatory vs. optional).

**Normalization Techniques**

- Understanding the Normalization Process
- Exploring the purpose of normalization in eliminating data redundancy.
- Studying the three normal forms: First Normal Form (1NF), Second Normal Form (2NF), and Third Normal Form (3NF).
- Learning how normalization reduces data anomalies and improves data integrity.
- Achieving First Normal Form (1NF)
  - Ensuring atomicity of data by eliminating repeating groups within tables.
  - Breaking down multi-valued attributes into separate tables.
- Achieving Second Normal Form (2NF)
  - Ensuring each non-key attribute is fully functionally dependent on the primary key.
  - Identifying and creating separate tables for partial dependencies.

- Achieving Third Normal Form (3NF)
  - Eliminating transitive dependencies by moving non-key attributes to separate tables.
  - Ensuring each non-key attribute depends only on the primary key.
- Additional Normal Forms (4NF, 5NF, BCNF)
  - Briefly introduce higher normal forms for advanced understanding, but not delving into their details.
  - Mention the purpose of these forms in achieving further data integrity.

**Weeks 5-6: Advanced SQL Queries and Joins**

**Advanced SQL Queries**

- Subqueries and Their Applications
  - Understanding subqueries and their role in complex data retrieval.
  - Writing subqueries within SELECT, FROM, and WHERE clauses.
  - Using subqueries for filtering, sorting, and aggregating data.
- Aggregating Data with GROUP BY and HAVING Clauses
  - Understanding the purpose of GROUP BY in data aggregation.
  - Aggregating data using functions such as SUM, COUNT, AVG, MAX, and MIN.
  - Filtering aggregated data using the HAVING clause.
- Working with Functions
  - Utilizing SQL functions for various purposes.
  - Commonly used functions like string manipulation, date and time functions, mathematical functions, etc.

**Joins**

- Understanding Different Types of Joins
  - Exploring INNER JOIN, LEFT JOIN, RIGHT JOIN, and FULL JOIN.
  - Knowing when to use each type of join based on data requirements.
  - Identifying join conditions and creating appropriate join queries.
- Joining Multiple Tables
  - Joining more than two tables using appropriate join clauses.
  - Handling complex join scenarios with multiple join conditions.
- Resolving Common Join Challenges
  - Handling duplicate records and understanding their causes.
  - Addressing NULL values and their impact on join results.
  - Using aliases to improve readability and avoid naming conflicts.

**Weeks 7-8: Stored Procedures and Functions**

**Stored Procedures**

- Creating and Executing Stored Procedures
  - Understanding the benefits and use cases of stored procedures.
  - Creating stored procedures in SQL.
  - Executing stored procedures using various methods.
- Parameterized Stored Procedures
  - Passing parameters to stored procedures.
  - Handling input and output parameters.
  - Implementing conditional logic and dynamic queries within stored procedures.
- Modifying Data and Performing Complex Operations
  - Using stored procedures for data manipulation (INSERT, UPDATE, DELETE).
  - Implementing complex business logic and calculations.
  - Incorporating error handling and exception management.

**User-Defined Functions**

- Different Types of Functions
  - Scalar functions for performing calculations on individual values.
  - Table-valued functions for returning result sets.
  - Inline and multi-statement functions.
- Creating and Using User-Defined Functions
  - Defining function parameters and return types.
  - Implementing reusable logic and calculations.
  - Integrating functions into SQL queries.

**Weeks 9-10: Data Manipulation**

**Data Manipulation**

- Inserting, Updating, and Deleting Data
  - Using SQL statements to insert new records.
  - Updating existing data with the UPDATE statement.
  - Deleting data using the DELETE statement.
- Modifying Existing Data
  - Using UPDATE statements to modify specific data.
  - Implementing data transformations and conversions.
  - Managing data integrity during updates.
- Transactions and Data Consistency
  - Understanding the concept of transactions.
  - Managing transactions using COMMIT and ROLLBACK.
  - Ensuring data consistency and integrity in multi-step operations.

**Weeks 11-12: Database Administration and Security**

**Database Administration**

- User Management and Access Control
  - Creating and managing user accounts and permissions.
  - Granting privileges for data access and manipulation.
- Backup and Recovery Procedures
  - Establishing backup strategies to protect data from loss or corruption.
  - Performing database backups and ensuring recoverability.
- Monitoring and Optimizing Database Performance
  - Implementing performance monitoring tools and techniques.
  - Identifying and resolving performance bottlenecks.
  - Optimizing query execution plans for better performance.

**Database Security**

- User Privileges and Roles
  - Understanding different levels of access control.
  - Assigning appropriate privileges and roles to users.
- Implementing Security Measures
  - Enforcing password policies and authentication mechanisms.
  - Applying encryption techniques to protect sensitive data.
- Protecting Against SQL Injection and Other Security Threats
  - Understanding common security vulnerabilities.
  - Implementing techniques to prevent SQL injection attacks.
  - Securing the database against unauthorized access.

**Resources**

- [DBFundamentals](https://learn.microsoft.com/en-us/shows/dbfundamentals/)
- [PostgresSQL Tutorials](https://neon.com/postgresql/tutorial)
- [Introduction to Relational Databases](https://www.coursera.org/learn/introduction-to-relational-databases)

<a name="capstone-projects"></a>
## Capstone Projects

Once fundamentals are covered and hands-on proficiency is achieved w.r.t. various database concepts, one can work on below case studies and produce demonstratable case studies below

**Project 1: Database Programmer - Library Management Application**

1. Database Schema Design:
  - Identify the entities involved in the library management application, such as books, library members, transactions, and so on.
  - Design the tables to represent these entities, defining the appropriate columns, data types, and relationships (e.g., primary and foreign keys).
  - Create the necessary constraints to enforce data integrity, such as unique constraints and foreign key constraints.
2. Table Creation:
  - Use SQL commands (e.g., CREATE TABLE statements) to create the required tables in the database.
  - Specify the columns, data types, constraints, and relationships for each table.
3. Data Population:
  - Insert sample data into the tables to simulate a library's data.
  - Populate the tables with book information, member details, and any other relevant data required for testing and development.
4. SQL Queries and Stored Procedures:
  - Implement SQL queries to retrieve specific information from the database, such as finding books by title or author, or retrieving borrowing history for a particular member.
  - Develop stored procedures to handle common tasks, such as borrowing or returning books, calculating late fees, and generating reports.
5. Testing and Validation:
  - Test the database system by executing various SQL queries and stored procedures to ensure they produce the expected results.
  - Validate data integrity and accuracy by verifying that the database constraints are properly enforced.

**Project 2: Database Engineer – WideWorldImporters Database Optimization**

Download and install the sample [Wide World Importers database](https://learn.microsoft.com/en-us/sql/samples/wide-world-importers-what-is?view=sql-server-ver16). Perform/Demonstrate below steps

1. Database Assessment:
  - Evaluate the existing database system, including the hardware infrastructure, database schema, and configuration settings.
  - Identify performance bottlenecks and areas for improvement.
2. Performance Analysis:
  - Analyze query execution plans to identify slow-running queries and potential performance issues.
  - Use database monitoring tools to track and analyze resource utilization, including CPU, memory, and disk I/O.
3. Query Optimization:
  - Identify problematic queries and analyze their structure and execution plans.
  - Rewrite queries to improve efficiency and reduce unnecessary operations.
  - Add or modify indexes to optimize query performance.
4. Configuration Optimization:
  - Review and modify database configuration settings, such as buffer pool size, cache settings, and parallel processing parameters, based on workload and system requirements.
5. Caching Implementation:
  - Identify frequently accessed data and implement caching mechanisms, such as in-memory caching or query result caching, to reduce database round trips and improve response times.
6. Load Testing and Performance Tuning:
  - Conduct load testing and stress testing to assess the database system's performance under various workload scenarios.
  - Monitor and analyze performance metrics during load testing to identify areas for improvement.
  - Optimize the database system based on the test results, making necessary adjustments to query structures, indexing strategies, and configuration settings.
7. Documentation and Recommendations:
  - Document the optimizations made, including the changes to queries, indexes, and configuration settings.
  - Provide recommendations for ongoing performance monitoring and maintenance to ensure the long-term efficiency of the database system.
