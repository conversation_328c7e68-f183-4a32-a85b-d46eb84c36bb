# Generative AI
 
Generative AI refers to a class of artificial intelligence models and techniques that are capable of generating new and original content, such as text, images, audio, or other forms of data. These models are trained on large datasets and use advanced algorithms, such as deep learning and neural networks, to learn patterns and relationships within the data.

If you're new to python, follow this [roadmap](https://roadmap.sh/python). Then cover the AI related concepts in [this course](https://www.deeplearning.ai/short-courses/ai-python-for-beginners/).

Following should be covered to get and understanding of the basics that are necessary to build Gen AI applications.

## Understanding LLMs

- [LLM deep dive](https://www.youtube.com/watch?v=7xTGNNLPyMI)
- [Generative AI with LLMs](https://www.deeplearning.ai/courses/generative-ai-with-llms/)

## Prompt Engineering

- [PE basics](https://developers.google.com/machine-learning/resources/prompt-eng)
- [PE Guide](https://learnprompting.org/docs/intro)
- [OpenAI Guide](https://cookbook.openai.com/examples/gpt4-1_prompting_guide)

### Advanced PE

- [Automated Prompt Optimization](https://towardsdatascience.com/automated-prompt-engineering-the-definitive-hands-on-guide-1476c8cd3c50/)
  - [Repo](https://github.com/heiko-hotz/automated-prompt-engineering-from-scratch)

## Context Engineering

The next evolution of prompt engineering

- [Introduction](https://blog.langchain.com/the-rise-of-context-engineering/)
- [Detailed overview](https://addyo.substack.com/p/context-engineering-bringing-engineering)

## Environment Setup

- Install [UV](https://docs.astral.sh/uv/getting-started/installation/)
- Navigate to the folder of choice and run `uv init`
- Install packages with `uv add <package>`
- Run a file with `uv run <file>.py`, this will automatically create a virtual environment, install dependencies and run the file.

## Working with LLM's

Finish parts 1-3 of [this workshop](https://github.com/philschmid/gemini-2.5-ai-engineering-workshop/tree/main) to get hands on experience with performing common tasks with LLM's.

One can also finish [this course](https://learn.deeplearning.ai/courses/large-multimodal-model-prompting-with-gemini) to get a better understanding of prompting best practices.

> **Note:** Don't work on Colab notebooks. Instead, create a proper development environment as described in the "Environment Setup" section above. This approach better simulates real-world project workflows.

## Retrieval-Augmented Generation

- Finish [this foundational course](https://www.coursera.org/learn/retrieval-augmented-generation-rag) to get hands on experience with RAG. The course can be autited for free.
- See [RAG Training Outline](RAG.md) for more details.

## AI Agents

See [AI Agents](Agents.md)

## Courses

- [Hugging Face Learn](https://hf.co/learn)
- [Deeplearning.ai](https://www.deeplearning.ai/courses/)

### MS Courses

Courses from Microsoft generally rely on Azure services and can't be practices without an Azure subscription. If one can replace vector db's with local/free alternatives and LLM calls with Gemini/Groq/Github Models that offer free quota, the courses can be completed without Azure subscription.

- [Generative AI](https://microsoft.github.io/generative-ai-for-beginners)
- [Agents](https://github.com/microsoft/ai-agents-for-beginners)
- [MCP](https://github.com/microsoft/mcp-for-beginners)
