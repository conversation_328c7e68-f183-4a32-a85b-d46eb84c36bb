# AI Agents

AI Agents are programs that are designed to plan and execute tasks independently without any pre-defined paths that traditional programs follow. They are capable of reasoning, planning, and executing tasks. They are also capable of learning from results of their task executions.

## Core Components of AI Agents

1. **Reasoning**: Analyzes situations and makes decisions using logic or LLMs.
2. **Planning**: Creates action sequences for goals, using deterministic or probabilistic methods.
3. **Tools/APIs**: Interacts with external systems (e.g., web, databases) to access data or perform tasks.
4. **Memory**:
   - **Short-Term**: Tracks context during tasks (e.g., conversation state).
   - **Long-Term**: Stores experiences (e.g., vector databases) for future use.
5. **Learning**: Improves via reinforcement learning or fine-tuning, adapting to new data.
6. **Interaction**: Collaborates with agents or environments via communication protocols or sensors.
7. **Execution**: Autonomously performs tasks with error-handling mechanisms.
8. **Contextual Awareness**: Monitors user preferences, environment, or history for tailored actions.

## Frameworks

- [LangGraph](https://www.langchain.com/langgraph)
- [CrewAI](https://www.crewai.com/)
- [Autogen](https://microsoft.github.io/autogen/stable/)
- [Google ADK](https://google.github.io/adk-docs/)
- [Agno](https://github.com/agno-agi/agno)

## Courses

- [LangGraph Agents](https://www.deeplearning.ai/short-courses/ai-agents-in-langgraph/)
- [CrewAI Agents](https://www.deeplearning.ai/short-courses/practical-multi-ai-agents-and-advanced-use-cases-with-crewai/)
- [HF Agents Course](https://huggingface.co/learn/agents-course)
- [LC RAG Agents](https://www.coursera.org/learn/fundamentals-of-ai-agents-using-rag-and-langchain)

## Hands-on Guides

- [GenAI Agents](https://github.com/NirDiamant/GenAI_Agents)
  - [Building Production Agents](https://github.com/NirDiamant/agents-towards-production)
- [Awesome LLM Apps Collection](https://github.com/Shubhamsaboo/awesome-llm-apps)

## Overall Approach

- [Evaluation Driven Agents](https://www.newsletter.swirlai.com/p/evaluation-driven-development-for)
- [12-factor-agents](https://github.com/humanlayer/12-factor-agents)

## General Guidelines

Building successful Agentic app comes from modular design, and observability that isn't an afterthought, and building in strong feedback loops so your Agent can improve over time. Below are 3 essential guidelines

🧠 **Modular & Role-Based Design**

- Instead of building one giant agent that does everything, break your system into smaller, role-specific agents.
- Use agent frameworks like CrewAI to orchestrate them.
- Assign clear responsibilities (e.g., researcher, planner, summarizer).
- Design to make debugging, testing, and optimization easier.

  👉 Why it matters: It increases scalability, improves interpretability, and avoids prompt spaghetti.

📡 **Deep Observability from Day 1**

- Don’t wait until things break. Integrate observability tools early. (pro tip: Use open-source eval tools like Comet Opik (https://lnkd.in/gsHdtNVb)!
- Track token usage, latency, success rates, and LLM inputs and outputs.
- Add LLM-as-a-judge evals to measure quality consistently.

  👉 Why it matters: You can’t optimize or trust what you can’t observe.

🔁 **Feedback Loops & Iterative Optimization**

- Your first prompt or architecture will be wrong. Build systems that can learn from failure.
- Collect user feedback, ratings, or internal evaluation signals.
- Feed this data back into your prompt tuning, RAG components, or agent routing logic.
- Use human-in-the-loop (HITL) or self-reflection patterns.

  👉 Why it matters: Continuous feedback is what turns an “okay” agent into a great one.
