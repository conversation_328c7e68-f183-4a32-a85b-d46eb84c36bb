# Training
This space will contain training artifacts, source code for training in various technology areas pertaining to Quabyt Technologies' products & services.

## General Guidance

The most important skill for all software engineers is - communication. Here is a good [short overview on how to improve your communication skills](https://www.youtube.com/watch?v=nj1AZoczVvg). Reading english books, watching english movies, etc helps build verbal and written communication skills.

### Trainees

Modern software development involves use of tools and techniques that greatly enhance individual productivity and team collaboration. One must get acquainted with those tools and techniques from the beginning.

In general, you never keep your development output just on your local machine. Get familiar with `version control & git` 
- [Doc](https://www.atlassian.com/git/tutorials/what-is-version-control)
- [Video](https://www.youtube.com/watch?v=vA5TTz6BXhY) 


#### Git workflow looks like below

![git workflow](img/git%20workflow.jpg)

Signin to [Github](https://github.com/) and create a [new private repository](https://docs.github.com/en/get-started/using-git/about-git#github-and-the-command-line) for each capstone project in a training plan. Invite mentors as [collaborators](https://docs.github.com/en/account-and-profile/setting-up-and-managing-your-personal-account-on-github/managing-access-to-your-personal-repositories/inviting-collaborators-to-a-personal-repository). 

Use of ChatGPT is optional but not recommended if you are learning the foundation of a technology like this training, for the reason that it's improbable that you will be able to judge the correctness/appropriateness of the solution provided by ChatGPT when you are also learning the nuances of a particular technology. Once you're familiar with the foundations, ChatGPT should be the first pass for implementing solution to any problem, thereafter you refine based on your experience.

One of the following can be finished for an understanding of computers & technology
- [CS50's Understanding Technology](https://cs50.harvard.edu/technology/2017/)
- [Freecodecamp's computer & technology basics](https://www.youtube.com/watch?v=y2kg3MOk1sY)

Then one can move to the following courses on programming basics

- [https://www.edx.org/course/introduction-computer-science-harvardx-cs50x](https://www.edx.org/course/introduction-computer-science-harvardx-cs50x)
- [https://www.coursera.org/specializations/introduction-computer-science-programming](https://www.coursera.org/specializations/introduction-computer-science-programming)

You can login to above with your google account. On coursera you can **audit** (full access without earning a certificate) any course for free. If you land up on a specialization (bundle of courses) on coursera, go to the **Courses** tab and select an individual course to audit it.

Here is an excellent free book to learn coding fundamentals
- [Nature of Code](https://natureofcode.com/)

Following mandatory courses need to be finished before embarking on stream aligned trainings

- [Introduction to Agile & Scrum](https://www.coursera.org/learn/agile-development-and-scrum)
- [Introduction to software testing](https://www.coursera.org/learn/introduction-software-testing)

### Mentors

Below are the general guidelines for being a good mentor
- Be approachable
- Set clear expectations and goals of a training
- Provide constructive feedback
- Foster a culture of collaboration and group activities
- Encourage hands-on learning
- Design problems that demand application of critical thinking abilities from trainees
- Setup regular check-ins on the progress, code reviews
- Follow a structured training plan
- Stay up-to-date with technology trends, last year's training plan may not apply anymore
- Use community driven [roadmaps](https://roadmap.sh/) for inspiration but have tailored training plans that align to company's needs

## Development Environment Setup

### IDE's

[VS Code](https://code.visualstudio.com/) is the [IDE](https://en.wikipedia.org/wiki/Integrated_development_environment) of choice for almost everything unless a better IDE is available like [Visual Studio for .NET development](https://visualstudio.microsoft.com/). VS Code offers functionalities such as syntax highlighting, code completion, debugging capabilities, version control integration, and an extensive ecosystem of extensions. It supports a wide range of programming languages and frameworks, making it versatile for different development tasks.

After installing VS Code, install the extensions (Go to the  **Extensions**  view (Ctrl+Shift+X)) below based on the training plan

### Foundation training plan

- [HTML CSS support](https://marketplace.visualstudio.com/items?itemName=ecmel.vscode-html-css)
- [CSS peek](https://marketplace.visualstudio.com/items?itemName=pranaygp.vscode-css-peek)
- [ESLint](https://marketplace.visualstudio.com/items?itemName=dbaeumer.vscode-eslint)
- [Auto rename tag](https://marketplace.visualstudio.com/items?itemName=formulahendry.auto-rename-tag)
- [Path intellisense](https://marketplace.visualstudio.com/items?itemName=christian-kohler.path-intellisense)
- [Prettier](https://marketplace.visualstudio.com/items?itemName=esbenp.prettier-vscode)
- [Live Server](https://marketplace.visualstudio.com/items?itemName=ritwickdey.LiveServer)
- [Intellicode](https://marketplace.visualstudio.com/items?itemName=VisualStudioExptTeam.vscodeintellicode)
- [GitLens](https://marketplace.visualstudio.com/items?itemName=eamodio.gitlens)
- [Import Cost](https://marketplace.visualstudio.com/items?itemName=wix.vscode-import-cost)

### React Training Plan

- [Simple react snippets](https://marketplace.visualstudio.com/items?itemName=burkeholland.simple-react-snippets)
- [ES7+ React/Redux/React-Native snippets](https://marketplace.visualstudio.com/items?itemName=dsznajder.es7-react-js-snippets)

### Angular Training Plan

- [Angular language service](https://marketplace.visualstudio.com/items?itemName=Angular.ng-template)
- [Angular snippets](https://marketplace.visualstudio.com/items?itemName=johnpapa.Angular2)

Other tools

- [Install powershell](https://learn.microsoft.com/en-us/powershell/scripting/install/installing-powershell?view=powershell-7.3)
- [Install nodejs](https://nodejs.org/en/download)
- Install node version manager (nvm)
  - [Windows](https://github.com/coreybutler/nvm-windows)
  - [Mac/Linux](https://github.com/nvm-sh/nvm)
- [Pdf reader](https://www.foxit.com/pdf-reader/)

### Working with container based workloads

- Install docker
- Install and configure local env with [Tilt](https://tilt.dev/)

For local kubernetes clusters use [KIND](https://kind.sigs.k8s.io/). Kubernetes can also be enabled in docker desktop but it only allows simulating one node, KIND on the other hand allows simulating multiple nodes (i.e. lets you create multi-node cluster).

## Issues

Some ISP's like Jio block raw github content, which means you can't download raw files, also images in readme files won't show up. To workaround this you will need to add custom DNS settings in your network adapter. Below are the steps

**Windows**: Go to "Control Panel" > "Network and Internet" > "Network and Sharing Center" > "Change adapter settings". Right-click on your network connection and select "Properties".

**macOS**: Go to "System Preferences" > "Network". Select your network connection from the list and click on the "Advanced" button.

**Linux**: The process can vary depending on the Linux distribution and desktop environment you're using. Generally, you can find network settings in the system settings or network manager.
Locate your network connection. It may be named "Ethernet," "Wi-Fi," or something similar.

Select the network connection and look for an option to edit its properties or settings.

In the network connection properties, look for an option related to DNS or IP settings.

**For IPv4 DNS settings**:
If there is a field for "DNS Server" or "Preferred DNS Server," enter the IPv4 address of your desired DNS server. You can use popular DNS servers like Google DNS (*******, *******) or Cloudflare DNS (*******, *******).
If there are multiple DNS server fields, you can enter multiple DNS server addresses for redundancy.

**For IPv6 DNS settings**:
If there is a field for "IPv6 DNS Server" or "Preferred IPv6 DNS Server," enter the IPv6 address of your desired DNS server. You can use popular IPv6 DNS servers like Google DNS (2001:4860:4860::8888, 2001:4860:4860::8844) or Cloudflare DNS (2606:4700:4700::1111, 2606:4700:4700::1001). If there are multiple IPv6 DNS server fields, you can enter multiple DNS server addresses for redundancy.

Save the changes and close the network settings.
