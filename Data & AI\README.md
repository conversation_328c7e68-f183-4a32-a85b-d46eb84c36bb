# Data & AI

Guidelines and training plans for Data & AI practice.

## Prerequisites

### Python

- [Roadmap](https://roadmap.sh/python)
- [Fundamentals Course](https://www.youtube.com/watch?v=eWRfhZUzrAc)

### FastAPI

- [Official Docs](https://fastapi.tiangolo.com/)
- [Crash Course](https://www.youtube.com/watch?v=rvFsGRvj9jo)
  - [Clean Architecture](https://www.youtube.com/watch?v=H9Blu0kWdZE)
- [FastAPI with MongoDB](https://www.mongodb.com/developer/languages/python/python-quickstart-fastapi/)
  - [Tutorial 2](https://www.youtube.com/watch?v=J7SXGbShbj8)
- [FastAPI with Relational Database](https://fastapi.tiangolo.com/tutorial/sql-databases/)
  - [FastAPI with PostgreSQL](https://blog.stackademic.com/python-building-simple-api-with-fastapi-and-postgresql-d5ddd7d501b7)

[Fullstack template](https://github.com/fastapi/full-stack-fastapi-template)

## Data Engineering

- [Fundamentals](/Data%20&%20AI/DatabaseEngineer.md)

## Data Science

## AI-ML

- [Fundamentals](https://cs50.harvard.edu/ai/2024/)

