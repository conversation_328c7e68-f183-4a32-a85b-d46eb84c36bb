# Evals for LLM Apps

## Resources

- [Guide to Evals](https://arize.com/llm-evaluation)
- [Langfuse guide](https://langfuse.com/blog/2025-03-04-llm-evaluation-101-best-practices-and-challenges)

## Eval Frameworks

- [DeepEval](https://github.com/confident-ai/deepeval)
- [Open Evals](https://github.com/langchain-ai/openevals)
- [OpenAI Evals](https://github.com/openai/evals)

## Platforms

- [Opik](https://www.comet.com/site/products/opik/)
- [Phoenix](https://phoenix.arize.com/)
- [Langfuse](https://langfuse.com/docs/scores/overview)

## Libraries

- [Lighteval](https://github.com/huggingface/lighteval)

## Tutorials

- [EvaluatingRAG with DeepEval](https://www.llamaindex.ai/blog/evaluating-rag-with-deepeval-and-llamaindex)
