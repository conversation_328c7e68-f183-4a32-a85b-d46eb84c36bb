# Quality Assurance

Quality assurance (QA) is a fundamental aspect of our operations at Quabyt, ensuring that every product we deliver meets the highest standards of excellence. QA is a systematic process that involves rigorous testing, meticulous evaluation, and continuous improvement to guarantee that our software solutions are reliable, functional, and user-friendly.

Mandatory QA courses for entry level candidates

- [Introduction to software testing](https://www.coursera.org/learn/introduction-software-testing)
- [Black box & White box testing](https://www.coursera.org/learn/black-box-white-box-testing)