# Azure Training Plan

## Courses

- Azure Fundamentals
  - [Cloud concepts](https://learn.microsoft.com/en-us/training/paths/microsoft-azure-fundamentals-describe-cloud-concepts/)
  - [Architecutre & Services](https://learn.microsoft.com/en-us/training/paths/azure-fundamentals-describe-azure-architecture-services/)
  - [Azure cloud services course](https://www.coursera.org/learn/microsoft-azure-cloud-services)

- Azure Functions
  - [Azure Functions for .NET Developers](https://www.youtube.com/watch?v=82QnxMp8PRY)

- Azure Service Bus
  - [Deep Dive](https://www.youtube.com/watch?v=LM7DByKOHBs)

- Azure Storage Services
  - [Overview](https://www.youtube.com/watch?v=_Qlkvd4ZQuo)

## Tools

- [Storage Explorer](https://azure.microsoft.com/en-us/products/storage/storage-explorer)
- [Azurite VS Code Extension](https://marketplace.visualstudio.com/items?itemName=Azurite.azurite)
  - [Azurite for Local development](https://learn.microsoft.com/en-us/azure/storage/common/storage-use-azurite)
  - Only install the VS Code extension and start individual servies from VS Code
